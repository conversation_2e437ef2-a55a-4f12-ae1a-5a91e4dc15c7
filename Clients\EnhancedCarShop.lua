-- EnhancedCarShop.lua - Modern car shop with 3D preview and advanced UI
-- Place this script in StarterPlayer > StarterPlayerScripts

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

-- Player references
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Remote events
local remotes = ReplicatedStorage:WaitForChild("RemoteEvents")
local purchaseRequest = remotes:WaitForChild("PurchaseRequest")
local spawnCarRequest = remotes:WaitForChild("SpawnCarRequest")

-- Car shop variables
local carShopGui = nil
local isShopOpen = false
local selectedCar = nil
local currentCategory = "All"
local carPreviewModel = nil
local viewportFrame = nil

-- Player data
local playerData = {
    Cash = 500,
    Level = 1,
    OwnedCars = {"BasicCar"}
}

-- Enhanced car configurations with 3D models
local carConfigs = {
    BasicCar = {
        Name = "Basic Car",
        Price = 0,
        Speed = 50,
        Handling = 60,
        Category = "Civilian",
        RequiredLevel = 1,
        Description = "A simple starter car for getting around the city.",
        ModelName = "BasicCarModel",
        Color = Color3.fromRGB(100, 100, 100)
    },
    
    FamilyCar = {
        Name = "Family Car",
        Price = 1500,
        Speed = 55,
        Handling = 65,
        Category = "Civilian",
        RequiredLevel = 3,
        Description = "Reliable family vehicle with good comfort.",
        ModelName = "FamilyCarModel",
        Color = Color3.fromRGB(150, 150, 200)
    },
    
    SportsCar = {
        Name = "Sports Car",
        Price = 5000,
        Speed = 85,
        Handling = 80,
        Category = "Civilian",
        RequiredLevel = 8,
        Description = "High-performance sports car for speed enthusiasts.",
        ModelName = "SportsCarModel",
        Color = Color3.fromRGB(255, 50, 50)
    },
    
    PoliceCruiser = {
        Name = "Police Cruiser",
        Price = 3000,
        Speed = 75,
        Handling = 75,
        Category = "Police",
        RequiredLevel = 5,
        Description = "Standard police vehicle with good all-around performance.",
        ModelName = "PoliceCruiserModel",
        Color = Color3.fromRGB(255, 255, 255)
    },
    
    SWATVehicle = {
        Name = "SWAT Vehicle",
        Price = 8000,
        Speed = 70,
        Handling = 85,
        Category = "Police",
        RequiredLevel = 12,
        Description = "Armored SWAT vehicle for high-risk operations.",
        ModelName = "SWATVehicleModel",
        Color = Color3.fromRGB(50, 50, 50),
        GamepassRequired = "SWATPack"
    },
    
    GetawayCar = {
        Name = "Getaway Car",
        Price = 4500,
        Speed = 90,
        Handling = 70,
        Category = "Criminal",
        RequiredLevel = 10,
        Description = "Fast escape vehicle perfect for quick getaways.",
        ModelName = "GetawayCarModel",
        Color = Color3.fromRGB(100, 50, 200)
    },
    
    Motorcycle = {
        Name = "Motorcycle",
        Price = 2000,
        Speed = 95,
        Handling = 60,
        Category = "Special",
        RequiredLevel = 6,
        Description = "Ultra-fast motorcycle with excellent acceleration.",
        ModelName = "MotorcycleModel",
        Color = Color3.fromRGB(255, 150, 0)
    },
    
    Supercar = {
        Name = "Supercar",
        Price = 15000,
        Speed = 100,
        Handling = 90,
        Category = "Special",
        RequiredLevel = 20,
        Description = "The ultimate luxury supercar with unmatched performance.",
        ModelName = "SupercarModel",
        Color = Color3.fromRGB(255, 215, 0),
        GamepassRequired = "SupercarPack"
    },
    
    LuxuryCar = {
        Name = "Luxury Car",
        Price = 12000,
        Speed = 80,
        Handling = 95,
        Category = "Special",
        RequiredLevel = 15,
        Description = "Premium luxury vehicle with superior handling.",
        ModelName = "LuxuryCarModel",
        Color = Color3.fromRGB(50, 50, 100),
        GamepassRequired = "LuxuryCarsPack"
    }
}

-- Create enhanced car shop GUI with modern design
local function createEnhancedCarShopGUI()
    -- Main screen GUI
    carShopGui = Instance.new("ScreenGui")
    carShopGui.Name = "EnhancedCarShopGUI"
    carShopGui.ResetOnSpawn = false
    carShopGui.Enabled = false
    carShopGui.Parent = playerGui
    
    -- Background blur effect
    local blurFrame = Instance.new("Frame")
    blurFrame.Name = "BlurBackground"
    blurFrame.Size = UDim2.new(1, 0, 1, 0)
    blurFrame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    blurFrame.BackgroundTransparency = 0.4
    blurFrame.BorderSizePixel = 0
    blurFrame.Parent = carShopGui
    
    -- Main container with modern design
    local containerFrame = Instance.new("Frame")
    containerFrame.Name = "ContainerFrame"
    containerFrame.Size = UDim2.new(0, 1400, 0, 800)
    containerFrame.Position = UDim2.new(0.5, -700, 0.5, -400)
    containerFrame.BackgroundColor3 = Color3.fromRGB(20, 20, 25)
    containerFrame.BorderSizePixel = 0
    containerFrame.Parent = carShopGui
    
    local containerCorner = Instance.new("UICorner")
    containerCorner.CornerRadius = UDim.new(0, 20)
    containerCorner.Parent = containerFrame
    
    -- Gradient background
    local backgroundGradient = Instance.new("UIGradient")
    backgroundGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(25, 25, 35)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(15, 15, 20))
    }
    backgroundGradient.Rotation = 135
    backgroundGradient.Parent = containerFrame
    
    -- Drop shadow
    local shadow = Instance.new("Frame")
    shadow.Name = "Shadow"
    shadow.Size = UDim2.new(1, 30, 1, 30)
    shadow.Position = UDim2.new(0, -15, 0, -15)
    shadow.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    shadow.BackgroundTransparency = 0.8
    shadow.BorderSizePixel = 0
    shadow.ZIndex = containerFrame.ZIndex - 1
    shadow.Parent = carShopGui
    
    local shadowCorner = Instance.new("UICorner")
    shadowCorner.CornerRadius = UDim.new(0, 20)
    shadowCorner.Parent = shadow
    
    -- Modern title bar with glass effect
    local titleBar = Instance.new("Frame")
    titleBar.Name = "TitleBar"
    titleBar.Size = UDim2.new(1, 0, 0, 80)
    titleBar.BackgroundColor3 = Color3.fromRGB(40, 120, 255)
    titleBar.BackgroundTransparency = 0.1
    titleBar.BorderSizePixel = 0
    titleBar.Parent = containerFrame
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 20)
    titleCorner.Parent = titleBar
    
    -- Title gradient
    local titleGradient = Instance.new("UIGradient")
    titleGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(60, 140, 255)),
        ColorSequenceKeypoint.new(0.5, Color3.fromRGB(40, 120, 255)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(20, 100, 235))
    }
    titleGradient.Rotation = 45
    titleGradient.Parent = titleBar
    
    -- Title bar bottom fix
    local titleFix = Instance.new("Frame")
    titleFix.Size = UDim2.new(1, 0, 0, 40)
    titleFix.Position = UDim2.new(0, 0, 1, -40)
    titleFix.BackgroundColor3 = Color3.fromRGB(20, 100, 235)
    titleFix.BorderSizePixel = 0
    titleFix.Parent = titleBar
    
    -- Title text with modern styling
    local titleText = Instance.new("TextLabel")
    titleText.Name = "TitleText"
    titleText.Size = UDim2.new(0, 400, 1, 0)
    titleText.Position = UDim2.new(0, 30, 0, 0)
    titleText.BackgroundTransparency = 1
    titleText.Text = "🚗 PREMIUM CAR DEALERSHIP"
    titleText.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleText.TextScaled = true
    titleText.Font = Enum.Font.GothamBold
    titleText.TextXAlignment = Enum.TextXAlignment.Left
    titleText.Parent = titleBar
    
    -- Modern close button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 50, 0, 50)
    closeButton.Position = UDim2.new(1, -65, 0, 15)
    closeButton.BackgroundColor3 = Color3.fromRGB(255, 60, 60)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.GothamBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 25)
    closeCorner.Parent = closeButton
    
    return containerFrame, closeButton
end

-- Create 3D car preview section
local function create3DPreviewSection(parent)
    -- Preview section frame
    local previewSection = Instance.new("Frame")
    previewSection.Name = "PreviewSection"
    previewSection.Size = UDim2.new(0.6, -20, 1, -100)
    previewSection.Position = UDim2.new(0, 10, 0, 90)
    previewSection.BackgroundColor3 = Color3.fromRGB(30, 30, 40)
    previewSection.BorderSizePixel = 0
    previewSection.Parent = parent

    local previewCorner = Instance.new("UICorner")
    previewCorner.CornerRadius = UDim.new(0, 15)
    previewCorner.Parent = previewSection

    -- Preview gradient
    local previewGradient = Instance.new("UIGradient")
    previewGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(35, 35, 45)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(25, 25, 35))
    }
    previewGradient.Rotation = 90
    previewGradient.Parent = previewSection

    -- 3D ViewportFrame for car preview
    viewportFrame = Instance.new("ViewportFrame")
    viewportFrame.Name = "CarPreview"
    viewportFrame.Size = UDim2.new(1, -40, 0.7, -20)
    viewportFrame.Position = UDim2.new(0, 20, 0, 20)
    viewportFrame.BackgroundColor3 = Color3.fromRGB(15, 15, 20)
    viewportFrame.BorderSizePixel = 0
    viewportFrame.Parent = previewSection

    local viewportCorner = Instance.new("UICorner")
    viewportCorner.CornerRadius = UDim.new(0, 12)
    viewportCorner.Parent = viewportFrame

    -- Viewport camera setup
    local camera = Instance.new("Camera")
    camera.Parent = viewportFrame
    viewportFrame.CurrentCamera = camera

    -- Car info panel below preview
    local infoPanel = Instance.new("Frame")
    infoPanel.Name = "InfoPanel"
    infoPanel.Size = UDim2.new(1, -40, 0.3, -40)
    infoPanel.Position = UDim2.new(0, 20, 0.7, 0)
    infoPanel.BackgroundColor3 = Color3.fromRGB(40, 40, 50)
    infoPanel.BorderSizePixel = 0
    infoPanel.Parent = previewSection

    local infoPanelCorner = Instance.new("UICorner")
    infoPanelCorner.CornerRadius = UDim.new(0, 12)
    infoPanelCorner.Parent = infoPanel

    -- Car name display
    local carNameLabel = Instance.new("TextLabel")
    carNameLabel.Name = "CarNameLabel"
    carNameLabel.Size = UDim2.new(1, -20, 0, 40)
    carNameLabel.Position = UDim2.new(0, 10, 0, 10)
    carNameLabel.BackgroundTransparency = 1
    carNameLabel.Text = "Select a car to preview"
    carNameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    carNameLabel.TextScaled = true
    carNameLabel.Font = Enum.Font.GothamBold
    carNameLabel.TextXAlignment = Enum.TextXAlignment.Left
    carNameLabel.Parent = infoPanel

    -- Car description
    local carDescLabel = Instance.new("TextLabel")
    carDescLabel.Name = "CarDescLabel"
    carDescLabel.Size = UDim2.new(1, -20, 0, 30)
    carDescLabel.Position = UDim2.new(0, 10, 0, 50)
    carDescLabel.BackgroundTransparency = 1
    carDescLabel.Text = "Choose a vehicle from the list to see details"
    carDescLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    carDescLabel.TextScaled = true
    carDescLabel.Font = Enum.Font.Gotham
    carDescLabel.TextXAlignment = Enum.TextXAlignment.Left
    carDescLabel.TextWrapped = true
    carDescLabel.Parent = infoPanel

    -- Stats display frame
    local statsFrame = Instance.new("Frame")
    statsFrame.Name = "StatsFrame"
    statsFrame.Size = UDim2.new(1, -20, 0, 60)
    statsFrame.Position = UDim2.new(0, 10, 0, 85)
    statsFrame.BackgroundTransparency = 1
    statsFrame.Parent = infoPanel

    -- Speed stat
    local speedLabel = Instance.new("TextLabel")
    speedLabel.Name = "SpeedLabel"
    speedLabel.Size = UDim2.new(0.5, -5, 0, 25)
    speedLabel.Position = UDim2.new(0, 0, 0, 0)
    speedLabel.BackgroundTransparency = 1
    speedLabel.Text = "🏎️ Speed: --"
    speedLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
    speedLabel.TextScaled = true
    speedLabel.Font = Enum.Font.GothamBold
    speedLabel.TextXAlignment = Enum.TextXAlignment.Left
    speedLabel.Parent = statsFrame

    -- Handling stat
    local handlingLabel = Instance.new("TextLabel")
    handlingLabel.Name = "HandlingLabel"
    handlingLabel.Size = UDim2.new(0.5, -5, 0, 25)
    handlingLabel.Position = UDim2.new(0.5, 5, 0, 0)
    handlingLabel.BackgroundTransparency = 1
    handlingLabel.Text = "🎯 Handling: --"
    handlingLabel.TextColor3 = Color3.fromRGB(100, 150, 255)
    handlingLabel.TextScaled = true
    handlingLabel.Font = Enum.Font.GothamBold
    handlingLabel.TextXAlignment = Enum.TextXAlignment.Left
    handlingLabel.Parent = statsFrame

    -- Price and level requirement
    local priceLabel = Instance.new("TextLabel")
    priceLabel.Name = "PriceLabel"
    priceLabel.Size = UDim2.new(0.5, -5, 0, 25)
    priceLabel.Position = UDim2.new(0, 0, 0, 30)
    priceLabel.BackgroundTransparency = 1
    priceLabel.Text = "💰 Price: $--"
    priceLabel.TextColor3 = Color3.fromRGB(255, 215, 0)
    priceLabel.TextScaled = true
    priceLabel.Font = Enum.Font.GothamBold
    priceLabel.TextXAlignment = Enum.TextXAlignment.Left
    priceLabel.Parent = statsFrame

    -- Level requirement
    local levelLabel = Instance.new("TextLabel")
    levelLabel.Name = "LevelLabel"
    levelLabel.Size = UDim2.new(0.5, -5, 0, 25)
    levelLabel.Position = UDim2.new(0.5, 5, 0, 30)
    levelLabel.BackgroundTransparency = 1
    levelLabel.Text = "⭐ Level: --"
    levelLabel.TextColor3 = Color3.fromRGB(255, 255, 100)
    levelLabel.TextScaled = true
    levelLabel.Font = Enum.Font.GothamBold
    levelLabel.TextXAlignment = Enum.TextXAlignment.Left
    levelLabel.Parent = statsFrame

    return previewSection, viewportFrame, infoPanel
end

-- Create car list section
local function createCarListSection(parent)
    -- Car list section frame
    local listSection = Instance.new("Frame")
    listSection.Name = "ListSection"
    listSection.Size = UDim2.new(0.4, -20, 1, -100)
    listSection.Position = UDim2.new(0.6, 10, 0, 90)
    listSection.BackgroundColor3 = Color3.fromRGB(30, 30, 40)
    listSection.BorderSizePixel = 0
    listSection.Parent = parent

    local listCorner = Instance.new("UICorner")
    listCorner.CornerRadius = UDim.new(0, 15)
    listCorner.Parent = listSection

    -- List gradient
    local listGradient = Instance.new("UIGradient")
    listGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(35, 35, 45)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(25, 25, 35))
    }
    listGradient.Rotation = 90
    listGradient.Parent = listSection

    -- Player info header
    local playerInfoFrame = Instance.new("Frame")
    playerInfoFrame.Name = "PlayerInfoFrame"
    playerInfoFrame.Size = UDim2.new(1, -20, 0, 80)
    playerInfoFrame.Position = UDim2.new(0, 10, 0, 10)
    playerInfoFrame.BackgroundColor3 = Color3.fromRGB(40, 120, 255)
    playerInfoFrame.BorderSizePixel = 0
    playerInfoFrame.Parent = listSection

    local playerInfoCorner = Instance.new("UICorner")
    playerInfoCorner.CornerRadius = UDim.new(0, 12)
    playerInfoCorner.Parent = playerInfoFrame

    -- Player info gradient
    local playerInfoGradient = Instance.new("UIGradient")
    playerInfoGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(60, 140, 255)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(40, 120, 255))
    }
    playerInfoGradient.Rotation = 45
    playerInfoGradient.Parent = playerInfoFrame

    return listSection, playerInfoFrame
end
