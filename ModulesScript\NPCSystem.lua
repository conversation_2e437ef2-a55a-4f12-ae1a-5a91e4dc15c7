-- NPCSystem.lua - NPC management and behavior system
local NPCSystem = {}

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local PathfindingService = game:GetService("PathfindingService")
local TweenService = game:GetService("TweenService")

-- NPC Types
NPCSystem.NPCTypes = {
    Criminal = {
        Name = "Criminal",
        Health = 100,
        WalkSpeed = 12,
        RunSpeed = 18,
        ArrestReward = 300,
        XPReward = 15,
        Behavior = "Wander",
        SpawnChance = 0.7
    },
    Civilian = {
        Name = "Civilian",
        Health = 100,
        WalkSpeed = 10,
        RunSpeed = 14,
        ArrestReward = 0,
        XPReward = 0,   
        Behavior = "Peaceful",
        SpawnChance = 0.3
    }
}

-- NPC Storage
NPCSystem.ActiveNPCs = {}
NPCSystem.NPCSpawnPoints = {}
NPCSystem.MaxNPCs = 8
NPCSystem.SpawnCooldown = 5 -- seconds between spawns

-- Events
NPCSystem.Events = {
    NPCSpawned = Instance.new("BindableEvent"),
    NPCArrested = Instance.new("BindableEvent"),
    NPCDestroyed = Instance.new("BindableEvent")
}

-- Initialize NPC System
function NPCSystem:Initialize()
    print("NPCSystem: Initializing...")
    
    -- Find NPC spawn points
    self:FindSpawnPoints()
    
    -- Start NPC management loop
    self:StartNPCLoop()
    
    print("NPCSystem: Initialized successfully!")
end

-- Find spawn points in workspace
function NPCSystem:FindSpawnPoints()
    local npcsFolder = workspace:FindFirstChild("NPCs")
    if not npcsFolder then
        warn("NPCSystem: NPCs folder not found in workspace")
        return
    end
    
    -- Clear existing spawn points
    self.NPCSpawnPoints = {}
    
    -- Find spawn point parts
    for _, child in ipairs(npcsFolder:GetChildren()) do
        if child:IsA("BasePart") and child:GetAttribute("SpawnPoint") then
            table.insert(self.NPCSpawnPoints, child)
        end
    end
    
    print("NPCSystem: Found", #self.NPCSpawnPoints, "spawn points")
end

-- Create NPC model
function NPCSystem:CreateNPCModel(npcType, position)
    local config = self.NPCTypes[npcType]
    if not config then
        warn("NPCSystem: Unknown NPC type:", npcType)
        return nil
    end
    
    -- Create NPC model
    local npc = Instance.new("Model")
    npc.Name = config.Name .. "_NPC_" .. tick()
    npc.Parent = workspace:FindFirstChild("NPCs") or workspace
    
    -- Create humanoid root part
    local rootPart = Instance.new("Part")
    rootPart.Name = "HumanoidRootPart"
    rootPart.Size = Vector3.new(2, 2, 1)
    rootPart.Material = Enum.Material.Plastic
    rootPart.BrickColor = BrickColor.new("Bright blue")
    rootPart.CanCollide = false
    rootPart.Anchored = false
    rootPart.CFrame = CFrame.new(position)
    rootPart.Parent = npc
    
    -- Create head
    local head = Instance.new("Part")
    head.Name = "Head"
    head.Size = Vector3.new(2, 1, 1)
    head.Material = Enum.Material.Plastic
    head.BrickColor = BrickColor.new("Light orange")
    head.CanCollide = false
    head.Parent = npc
    
    -- Weld head to root part
    local headWeld = Instance.new("WeldConstraint")
    headWeld.Part0 = rootPart
    headWeld.Part1 = head
    headWeld.Parent = rootPart
    
    -- Position head
    head.CFrame = rootPart.CFrame * CFrame.new(0, 1.5, 0)
    
    -- Create humanoid
    local humanoid = Instance.new("Humanoid")
    humanoid.MaxHealth = config.Health
    humanoid.Health = config.Health
    humanoid.WalkSpeed = config.WalkSpeed
    humanoid.Parent = npc
    
    -- Set primary part
    npc.PrimaryPart = rootPart
    
    -- Add NPC attributes
    npc:SetAttribute("NPCType", npcType)
    npc:SetAttribute("IsNPC", true)
    npc:SetAttribute("SpawnTime", tick())
    npc:SetAttribute("Behavior", config.Behavior)
    
    -- Add criminal indicator for criminal NPCs
    if npcType == "Criminal" then
        rootPart.BrickColor = BrickColor.new("Really red")
        
        -- Add criminal tag above head
        local billboardGui = Instance.new("BillboardGui")
        billboardGui.Size = UDim2.new(0, 100, 0, 50)
        billboardGui.StudsOffset = Vector3.new(0, 3, 0)
        billboardGui.Parent = head
        
        local label = Instance.new("TextLabel")
        label.Size = UDim2.new(1, 0, 1, 0)
        label.BackgroundTransparency = 1
        label.Text = "🚨 CRIMINAL"
        label.TextColor3 = Color3.fromRGB(255, 0, 0)
        label.TextScaled = true
        label.Font = Enum.Font.SourceSansBold
        label.Parent = billboardGui
    end
    
    return npc
end

-- Spawn NPC at random location
function NPCSystem:SpawnNPC(npcType, spawnPoint)
    if #self.ActiveNPCs >= self.MaxNPCs then
        return nil
    end
    
    local position
    if spawnPoint then
        position = spawnPoint.Position + Vector3.new(0, 5, 0)
    else
        -- Use random spawn point
        if #self.NPCSpawnPoints == 0 then
            warn("NPCSystem: No spawn points available")
            return nil
        end
        
        local randomSpawn = self.NPCSpawnPoints[math.random(1, #self.NPCSpawnPoints)]
        position = randomSpawn.Position + Vector3.new(0, 5, 0)
    end
    
    local npc = self:CreateNPCModel(npcType, position)
    if not npc then
        return nil
    end
    
    -- Add to active NPCs
    table.insert(self.ActiveNPCs, npc)
    
    -- Start NPC behavior
    self:StartNPCBehavior(npc)
    
    -- Fire event
    self.Events.NPCSpawned:Fire(npc, npcType)
    
    print("NPCSystem: Spawned", npcType, "NPC at", position)
    return npc
end

-- Start NPC behavior loop
function NPCSystem:StartNPCBehavior(npc)
    local npcType = npc:GetAttribute("NPCType")
    local behavior = npc:GetAttribute("Behavior")
    
    spawn(function()
        while npc.Parent and npc:FindFirstChild("Humanoid") do
            local humanoid = npc:FindFirstChild("Humanoid")
            
            if behavior == "Wander" then
                self:WanderBehavior(npc, humanoid)
            elseif behavior == "Peaceful" then
                self:PeacefulBehavior(npc, humanoid)
            end
            
            wait(math.random(3, 8)) -- Random behavior interval
        end
    end)
end

-- Wander behavior for criminal NPCs
function NPCSystem:WanderBehavior(npc, humanoid)
    if not npc.PrimaryPart then return end

    local behavior = math.random(1, 4)

    if behavior == 1 then
        -- Move toward crime locations
        self:MoveTowardCrimeLocation(npc, humanoid)
    elseif behavior == 2 then
        -- Avoid police players
        self:AvoidPolice(npc, humanoid)
    elseif behavior == 3 then
        -- Random suspicious movement
        self:SuspiciousMovement(npc, humanoid)
    else
        -- Normal wandering
        self:NormalWander(npc, humanoid)
    end
end

-- Move toward crime locations
function NPCSystem:MoveTowardCrimeLocation(npc, humanoid)
    local crimeLocations = workspace:FindFirstChild("CrimeLocations")
    if not crimeLocations then return end

    local locations = {}
    for _, location in ipairs(crimeLocations:GetChildren()) do
        if location:IsA("BasePart") then
            table.insert(locations, location)
        end
    end

    if #locations > 0 then
        local targetLocation = locations[math.random(1, #locations)]
        local targetPos = targetLocation.Position + Vector3.new(
            math.random(-10, 10), 0, math.random(-10, 10)
        )
        humanoid:MoveTo(targetPos)

        -- Simulate "committing crime" behavior
        npc:SetAttribute("Activity", "Investigating")
    end
end

-- Avoid police players
function NPCSystem:AvoidPolice(npc, humanoid)
    if not npc.PrimaryPart then return end

    local nearestPolice = self:FindNearestPolice(npc.PrimaryPart.Position)
    if nearestPolice and nearestPolice.distance < 30 then
        -- Run away from police
        local escapeDirection = (npc.PrimaryPart.Position - nearestPolice.player.Character.PrimaryPart.Position).Unit
        local escapePos = npc.PrimaryPart.Position + escapeDirection * 25

        humanoid.WalkSpeed = self.NPCTypes.Criminal.RunSpeed
        humanoid:MoveTo(escapePos)

        npc:SetAttribute("Activity", "Fleeing")

        -- Return to normal speed after escaping
        spawn(function()
            wait(5)
            if humanoid and humanoid.Parent then
                humanoid.WalkSpeed = self.NPCTypes.Criminal.WalkSpeed
                npc:SetAttribute("Activity", "Wandering")
            end
        end)
    else
        self:NormalWander(npc, humanoid)
    end
end

-- Suspicious movement patterns
function NPCSystem:SuspiciousMovement(npc, humanoid)
    if not npc.PrimaryPart then return end

    -- Move in erratic patterns
    local currentPos = npc.PrimaryPart.Position
    local randomOffset = Vector3.new(
        math.random(-15, 15),
        0,
        math.random(-15, 15)
    )
    local targetPos = currentPos + randomOffset

    -- Occasionally sprint
    if math.random() < 0.4 then
        humanoid.WalkSpeed = self.NPCTypes.Criminal.RunSpeed
        npc:SetAttribute("Activity", "Suspicious")

        spawn(function()
            wait(math.random(2, 4))
            if humanoid and humanoid.Parent then
                humanoid.WalkSpeed = self.NPCTypes.Criminal.WalkSpeed
            end
        end)
    end

    humanoid:MoveTo(targetPos)
end

-- Normal wandering
function NPCSystem:NormalWander(npc, humanoid)
    if not npc.PrimaryPart then return end

    local currentPos = npc.PrimaryPart.Position
    local randomOffset = Vector3.new(
        math.random(-20, 20),
        0,
        math.random(-20, 20)
    )
    local targetPos = currentPos + randomOffset

    humanoid:MoveTo(targetPos)
    npc:SetAttribute("Activity", "Wandering")
end

-- Find nearest police player
function NPCSystem:FindNearestPolice(position)
    local nearestPolice = nil
    local nearestDistance = math.huge

    -- Get GameManager to check player roles
    local GameManager = require(ReplicatedStorage:WaitForChild("ModulesScript"):WaitForChild("GameManager"))

    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character.PrimaryPart then
            local role = GameManager:GetPlayerRole(player)
            if role == "Police" then
                local distance = (player.Character.PrimaryPart.Position - position).Magnitude
                if distance < nearestDistance then
                    nearestDistance = distance
                    nearestPolice = {player = player, distance = distance}
                end
            end
        end
    end

    return nearestPolice
end

-- Peaceful behavior for civilian NPCs
function NPCSystem:PeacefulBehavior(npc, humanoid)
    if not npc.PrimaryPart then return end
    
    -- Find random nearby position (smaller range)
    local currentPos = npc.PrimaryPart.Position
    local randomOffset = Vector3.new(
        math.random(-10, 10),
        0,
        math.random(-10, 10)
    )
    local targetPos = currentPos + randomOffset
    
    -- Move slowly to target
    humanoid:MoveTo(targetPos)
end

-- Arrest NPC (called by police)
function NPCSystem:ArrestNPC(npc, policePlayer)
    local npcType = npc:GetAttribute("NPCType")
    
    -- Only criminals can be arrested
    if npcType ~= "Criminal" then
        return false, "Cannot arrest civilians"
    end
    
    local config = self.NPCTypes[npcType]
    
    -- Remove NPC from active list
    for i, activeNPC in ipairs(self.ActiveNPCs) do
        if activeNPC == npc then
            table.remove(self.ActiveNPCs, i)
            break
        end
    end
    
    -- Fire arrest event
    self.Events.NPCArrested:Fire(npc, policePlayer, config.ArrestReward, config.XPReward)
    
    -- Destroy NPC
    npc:Destroy()
    
    print("NPCSystem:", policePlayer.Name, "arrested NPC criminal")
    return true, "NPC Criminal arrested!", config.ArrestReward, config.XPReward
end

-- Start NPC management loop
function NPCSystem:StartNPCLoop()
    spawn(function()
        while true do
            wait(self.SpawnCooldown)
            
            -- Clean up destroyed NPCs
            self:CleanupNPCs()
            
            -- Spawn new NPCs if needed
            if #self.ActiveNPCs < self.MaxNPCs then
                self:SpawnRandomNPC()
            end
        end
    end)
end

-- Spawn random NPC based on spawn chances
function NPCSystem:SpawnRandomNPC()
    local rand = math.random()
    local npcType = "Civilian"
    
    if rand < self.NPCTypes.Criminal.SpawnChance then
        npcType = "Criminal"
    end
    
    self:SpawnNPC(npcType)
end

-- Clean up destroyed NPCs
function NPCSystem:CleanupNPCs()
    for i = #self.ActiveNPCs, 1, -1 do
        local npc = self.ActiveNPCs[i]
        if not npc.Parent or not npc:FindFirstChild("Humanoid") then
            table.remove(self.ActiveNPCs, i)
        end
    end
end

-- Get nearest NPC to position
function NPCSystem:GetNearestNPC(position, maxDistance)
    local nearestNPC = nil
    local nearestDistance = maxDistance or math.huge
    
    for _, npc in ipairs(self.ActiveNPCs) do
        if npc.PrimaryPart then
            local distance = (npc.PrimaryPart.Position - position).Magnitude
            if distance < nearestDistance then
                nearestDistance = distance
                nearestNPC = npc
            end
        end
    end
    
    return nearestNPC, nearestDistance
end

-- Get all NPCs of specific type
function NPCSystem:GetNPCsByType(npcType)
    local npcs = {}
    for _, npc in ipairs(self.ActiveNPCs) do
        if npc:GetAttribute("NPCType") == npcType then
            table.insert(npcs, npc)
        end
    end
    return npcs
end

-- Destroy all NPCs
function NPCSystem:DestroyAllNPCs()
    for _, npc in ipairs(self.ActiveNPCs) do
        if npc.Parent then
            npc:Destroy()
        end
    end
    self.ActiveNPCs = {}
    print("NPCSystem: Destroyed all NPCs")
end

return NPCSystem
