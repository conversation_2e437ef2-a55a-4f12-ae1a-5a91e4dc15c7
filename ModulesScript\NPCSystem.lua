-- NPCSystem.lua - NPC management and behavior system
local NPCSystem = {}

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local PathfindingService = game:GetService("PathfindingService")
local TweenService = game:GetService("TweenService")

-- NPC Types
NPCSystem.NPCTypes = {
    Criminal = {
        Name = "Criminal",
        Health = 100,
        WalkSpeed = 12,
        RunSpeed = 18,
        ArrestReward = 300,
        XPReward = 15,
        Behavior = "Wander",
        SpawnChance = 0.7
    },
    Civilian = {
        Name = "Civilian",
        Health = 100,
        WalkSpeed = 10,
        RunSpeed = 14,
        ArrestReward = 0,
        XPReward = 0,   
        Behavior = "Peaceful",
        SpawnChance = 0.3
    }
}

-- NPC Storage
NPCSystem.ActiveNPCs = {}
NPCSystem.NPCSpawnPoints = {}
NPCSystem.MaxNPCs = 8
NPCSystem.SpawnCooldown = 5 -- seconds between spawns

-- Events
NPCSystem.Events = {
    NPCSpawned = Instance.new("BindableEvent"),
    NPCArrested = Instance.new("BindableEvent"),
    NPCDestroyed = Instance.new("BindableEvent")
}

-- Initialize NPC System
function NPCSystem:Initialize()
    print("NPCSystem: Initializing...")
    
    -- Find NPC spawn points
    self:FindSpawnPoints()
    
    -- Start NPC management loop
    self:StartNPCLoop()
    
    print("NPCSystem: Initialized successfully!")
end

-- Find spawn points and existing NPCs in workspace
function NPCSystem:FindSpawnPoints()
    local npcsFolder = workspace:FindFirstChild("NPCs")
    if not npcsFolder then
        warn("NPCSystem: NPCs folder not found in workspace")
        return
    end

    -- Clear existing spawn points
    self.NPCSpawnPoints = {}

    -- Find spawn point parts and existing NPC models
    for _, child in ipairs(npcsFolder:GetChildren()) do
        if child:IsA("BasePart") and child:GetAttribute("SpawnPoint") then
            table.insert(self.NPCSpawnPoints, child)
        elseif child:IsA("Model") and child:FindFirstChild("Humanoid") then
            -- Found existing NPC model - set it up for the system
            self:SetupExistingNPC(child)
        end
    end

    print("NPCSystem: Found", #self.NPCSpawnPoints, "spawn points and", #self.ActiveNPCs, "existing NPCs")
end

-- Setup existing NPC model for the system
function NPCSystem:SetupExistingNPC(npc)
    -- Check if NPC is already set up
    if npc:GetAttribute("IsNPC") then
        table.insert(self.ActiveNPCs, npc)
        return
    end

    -- Determine NPC type based on name or set default
    local npcType = "Criminal" -- Default to Criminal
    if npc.Name:lower():find("civilian") or npc.Name:lower():find("citizen") then
        npcType = "Civilian"
    end

    -- Set up NPC attributes
    npc:SetAttribute("NPCType", npcType)
    npc:SetAttribute("IsNPC", true)
    npc:SetAttribute("SpawnTime", tick())
    npc:SetAttribute("Behavior", self.NPCTypes[npcType].Behavior)
    npc:SetAttribute("Activity", "Wandering")

    -- Configure humanoid
    local humanoid = npc:FindFirstChild("Humanoid")
    if humanoid then
        local config = self.NPCTypes[npcType]
        humanoid.MaxHealth = config.Health
        humanoid.Health = config.Health
        humanoid.WalkSpeed = config.WalkSpeed
    end

    -- Add visual indicators for criminals
    if npcType == "Criminal" then
        self:AddCriminalIndicator(npc)
    end

    -- Add to active NPCs
    table.insert(self.ActiveNPCs, npc)

    -- Start behavior
    self:StartNPCBehavior(npc)

    print("NPCSystem: Set up existing NPC:", npc.Name, "as", npcType)
end

-- Add criminal indicator to NPC
function NPCSystem:AddCriminalIndicator(npc)
    local head = npc:FindFirstChild("Head")
    if not head then return end

    -- Change color if it's a basic part
    if head:IsA("BasePart") then
        head.BrickColor = BrickColor.new("Really red")
    end

    -- Add criminal tag above head
    local existingGui = head:FindFirstChild("CriminalTag")
    if existingGui then return end -- Already has tag

    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Name = "CriminalTag"
    billboardGui.Size = UDim2.new(0, 100, 0, 50)
    billboardGui.StudsOffset = Vector3.new(0, 3, 0)
    billboardGui.Parent = head

    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🚨 CRIMINAL"
    label.TextColor3 = Color3.fromRGB(255, 0, 0)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSansBold
    label.Parent = billboardGui
end

-- Note: CreateNPCModel function removed - only using existing NPC models from workspace

-- Activate existing NPC (ONLY use pre-made models)
function NPCSystem:SpawnNPC(npcType, spawnPoint)
    if #self.ActiveNPCs >= self.MaxNPCs then
        return nil
    end

    -- Only find and reactivate existing NPCs - NO creation
    local npc = self:FindInactiveNPC(npcType)

    if npc then
        -- Reactivate existing NPC
        self:ReactivateNPC(npc, spawnPoint)
        print("NPCSystem: Reactivated existing", npcType, "NPC:", npc.Name)
        return npc
    else
        -- Don't create new NPCs - only use existing models
        print("NPCSystem: No inactive", npcType, "NPCs available to reactivate")
        return nil
    end
end

-- Find inactive NPC of specified type
function NPCSystem:FindInactiveNPC(npcType)
    local npcsFolder = workspace:FindFirstChild("NPCs")
    if not npcsFolder then return nil end

    for _, child in ipairs(npcsFolder:GetChildren()) do
        if child:IsA("Model") and child:FindFirstChild("Humanoid") then
            local childType = child:GetAttribute("NPCType")
            local isActive = false

            -- Check if this NPC is in active list
            for _, activeNPC in ipairs(self.ActiveNPCs) do
                if activeNPC == child then
                    isActive = true
                    break
                end
            end

            -- If it matches type and is not active, we can use it
            if not isActive and (not npcType or childType == npcType) then
                return child
            end
        end
    end

    return nil
end

-- Reactivate an existing NPC
function NPCSystem:ReactivateNPC(npc, spawnPoint)
    -- Move to spawn point if specified
    if spawnPoint and npc.PrimaryPart then
        npc:SetPrimaryPartCFrame(CFrame.new(spawnPoint.Position + Vector3.new(0, 5, 0)))
    end

    -- Reset NPC state
    npc:SetAttribute("SpawnTime", tick())
    npc:SetAttribute("Activity", "Wandering")

    -- Reset humanoid
    local humanoid = npc:FindFirstChild("Humanoid")
    if humanoid then
        local npcType = npc:GetAttribute("NPCType")
        local config = self.NPCTypes[npcType]
        if config then
            humanoid.Health = config.Health
            humanoid.WalkSpeed = config.WalkSpeed
        end
    end

    -- Add to active NPCs
    table.insert(self.ActiveNPCs, npc)

    -- Start behavior
    self:StartNPCBehavior(npc)

    -- Fire event
    self.Events.NPCSpawned:Fire(npc, npc:GetAttribute("NPCType"))
end

-- Note: CreateNewNPC function removed - only using existing NPC models

-- Start NPC behavior loop
function NPCSystem:StartNPCBehavior(npc)
    local npcType = npc:GetAttribute("NPCType")
    local behavior = npc:GetAttribute("Behavior")
    
    spawn(function()
        while npc.Parent and npc:FindFirstChild("Humanoid") do
            local humanoid = npc:FindFirstChild("Humanoid")
            
            if behavior == "Wander" then
                self:WanderBehavior(npc, humanoid)
            elseif behavior == "Peaceful" then
                self:PeacefulBehavior(npc, humanoid)
            end
            
            wait(math.random(3, 8)) -- Random behavior interval
        end
    end)
end

-- Wander behavior for criminal NPCs
function NPCSystem:WanderBehavior(npc, humanoid)
    if not npc.PrimaryPart then return end

    local behavior = math.random(1, 4)

    if behavior == 1 then
        -- Move toward crime locations
        self:MoveTowardCrimeLocation(npc, humanoid)
    elseif behavior == 2 then
        -- Avoid police players
        self:AvoidPolice(npc, humanoid)
    elseif behavior == 3 then
        -- Random suspicious movement
        self:SuspiciousMovement(npc, humanoid)
    else
        -- Normal wandering
        self:NormalWander(npc, humanoid)
    end
end

-- Move toward crime locations
function NPCSystem:MoveTowardCrimeLocation(npc, humanoid)
    local crimeLocations = workspace:FindFirstChild("CrimeLocations")
    if not crimeLocations then return end

    local locations = {}
    for _, location in ipairs(crimeLocations:GetChildren()) do
        if location:IsA("BasePart") then
            table.insert(locations, location)
        end
    end

    if #locations > 0 then
        local targetLocation = locations[math.random(1, #locations)]
        local targetPos = targetLocation.Position + Vector3.new(
            math.random(-10, 10), 0, math.random(-10, 10)
        )
        humanoid:MoveTo(targetPos)

        -- Simulate "committing crime" behavior
        npc:SetAttribute("Activity", "Investigating")
    end
end

-- Avoid police players
function NPCSystem:AvoidPolice(npc, humanoid)
    if not npc.PrimaryPart then return end

    local nearestPolice = self:FindNearestPolice(npc.PrimaryPart.Position)
    if nearestPolice and nearestPolice.distance < 30 then
        -- Run away from police
        local escapeDirection = (npc.PrimaryPart.Position - nearestPolice.player.Character.PrimaryPart.Position).Unit
        local escapePos = npc.PrimaryPart.Position + escapeDirection * 25

        humanoid.WalkSpeed = self.NPCTypes.Criminal.RunSpeed
        humanoid:MoveTo(escapePos)

        npc:SetAttribute("Activity", "Fleeing")

        -- Return to normal speed after escaping
        spawn(function()
            wait(5)
            if humanoid and humanoid.Parent then
                humanoid.WalkSpeed = self.NPCTypes.Criminal.WalkSpeed
                npc:SetAttribute("Activity", "Wandering")
            end
        end)
    else
        self:NormalWander(npc, humanoid)
    end
end

-- Suspicious movement patterns
function NPCSystem:SuspiciousMovement(npc, humanoid)
    if not npc.PrimaryPart then return end

    -- Move in erratic patterns
    local currentPos = npc.PrimaryPart.Position
    local randomOffset = Vector3.new(
        math.random(-15, 15),
        0,
        math.random(-15, 15)
    )
    local targetPos = currentPos + randomOffset

    -- Occasionally sprint
    if math.random() < 0.4 then
        humanoid.WalkSpeed = self.NPCTypes.Criminal.RunSpeed
        npc:SetAttribute("Activity", "Suspicious")

        spawn(function()
            wait(math.random(2, 4))
            if humanoid and humanoid.Parent then
                humanoid.WalkSpeed = self.NPCTypes.Criminal.WalkSpeed
            end
        end)
    end

    humanoid:MoveTo(targetPos)
end

-- Normal wandering
function NPCSystem:NormalWander(npc, humanoid)
    if not npc.PrimaryPart then return end

    local currentPos = npc.PrimaryPart.Position
    local randomOffset = Vector3.new(
        math.random(-20, 20),
        0,
        math.random(-20, 20)
    )
    local targetPos = currentPos + randomOffset

    humanoid:MoveTo(targetPos)
    npc:SetAttribute("Activity", "Wandering")
end

-- Find nearest police player
function NPCSystem:FindNearestPolice(position)
    local nearestPolice = nil
    local nearestDistance = math.huge

    -- Get GameManager to check player roles
    local GameManager = require(ReplicatedStorage:WaitForChild("ModulesScript"):WaitForChild("GameManager"))

    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character.PrimaryPart then
            local role = GameManager:GetPlayerRole(player)
            if role == "Police" then
                local distance = (player.Character.PrimaryPart.Position - position).Magnitude
                if distance < nearestDistance then
                    nearestDistance = distance
                    nearestPolice = {player = player, distance = distance}
                end
            end
        end
    end

    return nearestPolice
end

-- Peaceful behavior for civilian NPCs
function NPCSystem:PeacefulBehavior(npc, humanoid)
    if not npc.PrimaryPart then return end
    
    -- Find random nearby position (smaller range)
    local currentPos = npc.PrimaryPart.Position
    local randomOffset = Vector3.new(
        math.random(-10, 10),
        0,
        math.random(-10, 10)
    )
    local targetPos = currentPos + randomOffset
    
    -- Move slowly to target
    humanoid:MoveTo(targetPos)
end

-- Arrest NPC (called by police)
function NPCSystem:ArrestNPC(npc, policePlayer)
    local npcType = npc:GetAttribute("NPCType")
    
    -- Only criminals can be arrested
    if npcType ~= "Criminal" then
        return false, "Cannot arrest civilians"
    end
    
    local config = self.NPCTypes[npcType]
    
    -- Remove NPC from active list
    for i, activeNPC in ipairs(self.ActiveNPCs) do
        if activeNPC == npc then
            table.remove(self.ActiveNPCs, i)
            break
        end
    end
    
    -- Fire arrest event
    self.Events.NPCArrested:Fire(npc, policePlayer, config.ArrestReward, config.XPReward)
    
    -- Destroy NPC
    npc:Destroy()
    
    print("NPCSystem:", policePlayer.Name, "arrested NPC criminal")
    return true, "NPC Criminal arrested!", config.ArrestReward, config.XPReward
end

-- Start NPC management loop
function NPCSystem:StartNPCLoop()
    spawn(function()
        while true do
            wait(self.SpawnCooldown)
            
            -- Clean up destroyed NPCs
            self:CleanupNPCs()
            
            -- Spawn new NPCs if needed
            if #self.ActiveNPCs < self.MaxNPCs then
                self:SpawnRandomNPC()
            end
        end
    end)
end

-- Activate random existing NPC
function NPCSystem:SpawnRandomNPC()
    -- Try to activate any available inactive NPC (any type)
    local npc = self:FindInactiveNPC(nil) -- nil means any type

    if npc then
        self:ReactivateNPC(npc, nil)
        print("NPCSystem: Activated random existing NPC:", npc.Name)
        return npc
    else
        print("NPCSystem: No inactive NPCs available to activate")
        return nil
    end
end

-- Clean up destroyed NPCs
function NPCSystem:CleanupNPCs()
    for i = #self.ActiveNPCs, 1, -1 do
        local npc = self.ActiveNPCs[i]
        if not npc.Parent or not npc:FindFirstChild("Humanoid") then
            table.remove(self.ActiveNPCs, i)
        end
    end
end

-- Get nearest NPC to position
function NPCSystem:GetNearestNPC(position, maxDistance)
    local nearestNPC = nil
    local nearestDistance = maxDistance or math.huge
    
    for _, npc in ipairs(self.ActiveNPCs) do
        if npc.PrimaryPart then
            local distance = (npc.PrimaryPart.Position - position).Magnitude
            if distance < nearestDistance then
                nearestDistance = distance
                nearestNPC = npc
            end
        end
    end
    
    return nearestNPC, nearestDistance
end

-- Get all NPCs of specific type
function NPCSystem:GetNPCsByType(npcType)
    local npcs = {}
    for _, npc in ipairs(self.ActiveNPCs) do
        if npc:GetAttribute("NPCType") == npcType then
            table.insert(npcs, npc)
        end
    end
    return npcs
end

-- Destroy all NPCs
function NPCSystem:DestroyAllNPCs()
    for _, npc in ipairs(self.ActiveNPCs) do
        if npc.Parent then
            npc:Destroy()
        end
    end
    self.ActiveNPCs = {}
    print("NPCSystem: Destroyed all NPCs")
end

return NPCSystem
