# 🤖 Using Your Existing NPC Models - Setup Guide

## ✅ **System Updated - No New NPCs Created!**

The NPCSystem has been modified to **ONLY** use your existing NPC models and will **NEVER** create new ones with Instance.new().

## 📁 **How It Works Now:**

### **1. Auto-Detection**
- Scans your `Workspace/NPCs` folder for existing Model objects
- Finds any Model that has a Humanoid inside
- Automatically sets up these models for the crime game

### **2. NPC Type Detection**
```lua
-- Automatic type assignment based on name:
if npc.Name:lower():find("civilian") or npc.Name:lower():find("citizen") then
    npcType = "Civilian"  -- Cannot be arrested
else
    npcType = "Criminal"  -- Can be arrested by police
end
```

### **3. What Gets Added to Your NPCs:**
- ✅ **Attributes**: `IsNPC = true`, `NPCType`, `Behavior`, etc.
- ✅ **AI Behavior**: Smart movement and actions
- ✅ **Criminal Tags**: Red "🚨 CRIMINAL" labels for arrestable NPCs
- ✅ **Arrest Functionality**: Police can arrest them with E key

## 🎯 **Your NPC Models Requirements:**

### **Minimum Requirements:**
1. **Model object** in `Workspace/NPCs` folder
2. **Humanoid** inside the model
3. **PrimaryPart** set (recommended for movement)

### **Optional for Better Results:**
- **Head part** (for criminal tags)
- **HumanoidRootPart** (for better movement)
- **Proper character structure** (for animations)

## 🏷️ **NPC Naming Convention:**

### **Criminal NPCs** (Arrestable):
- `Criminal1`, `Bandit`, `Thief`, `BadGuy`, etc.
- Any name WITHOUT "civilian" or "citizen"

### **Civilian NPCs** (Not Arrestable):
- `Civilian1`, `Citizen2`, `NormalGuy`, etc.
- Names containing "civilian" or "citizen"

## 🎮 **What Happens When Game Starts:**

```
NPCSystem: Found 0 spawn points and 5 existing NPCs
NPCSystem: Set up existing NPC: Criminal1 as Criminal
NPCSystem: Set up existing NPC: Civilian1 as Civilian
NPCSystem: Set up existing NPC: Bandit as Criminal
NPCSystem: Activated random existing NPC: Criminal1
```

## 🔄 **NPC Activation System:**

### **Instead of Creating New NPCs:**
1. **Finds Inactive NPCs**: Looks for NPCs not currently active
2. **Reactivates Them**: Moves them to spawn points and starts AI
3. **Cycles Through**: Reuses your existing models repeatedly
4. **No Duplication**: Never creates copies or new instances

### **Activation Process:**
```lua
-- Your NPC gets:
npc:SetAttribute("IsNPC", true)
npc:SetAttribute("NPCType", "Criminal") -- or "Civilian"
npc:SetAttribute("SpawnTime", tick())
npc:SetAttribute("Activity", "Wandering")

-- Criminal NPCs also get:
-- Red criminal tag above head
-- Arrestable status
```

## 🚨 **Criminal NPC Features:**

### **Visual Indicators:**
- **Red "🚨 CRIMINAL" tag** above head
- **Red coloring** on head part (if applicable)

### **AI Behaviors:**
- **Wander around** the map
- **Investigate crime locations** (banks, stores)
- **Avoid police players** when they get close
- **Suspicious movement** patterns

### **Arrest Mechanics:**
- **Police players** can press E within 15 studs
- **Rewards**: 300 cash + 15 XP per arrest
- **NPC disappears** from active list when arrested
- **Can be reactivated** later in the cycle

## 👤 **Civilian NPC Features:**

### **Peaceful Behavior:**
- **Calm wandering** in small areas
- **Cannot be arrested** by police
- **Adds atmosphere** to the game

## ⚙️ **Configuration:**

### **In NPCSystem.lua:**
```lua
NPCSystem.MaxNPCs = 8              -- Max active at once
NPCSystem.SpawnCooldown = 5        -- Seconds between activations

-- Criminal rewards:
ArrestReward = 300,                -- Cash for arresting
XPReward = 15,                     -- XP for arresting
```

## 🔧 **No Setup Required:**

### **Just Make Sure:**
1. ✅ Your NPC models are in `Workspace/NPCs`
2. ✅ Each model has a Humanoid
3. ✅ Models are named appropriately
4. ✅ NPCSystem.lua is in `ReplicatedStorage/ModulesScript`

### **The System Will:**
- ✅ **Auto-detect** all your NPCs
- ✅ **Set them up** automatically
- ✅ **Start their AI** behavior
- ✅ **Make them arrestable** (if criminal)
- ✅ **Cycle through them** as needed

## 🎉 **Result:**

Your existing NPC models will become intelligent, interactive characters that:
- **Move around** with AI behavior
- **Can be arrested** by police (if criminal)
- **Give rewards** when arrested
- **Add life** to your crime game

**No new NPCs created - only your existing models enhanced! 🎮**
