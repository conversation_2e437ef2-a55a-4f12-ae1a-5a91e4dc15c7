# 🎮 Crime Game - Complete Setup Instructions

## 📁 Script Placement Guide

### 1. ServerScriptService
Place these scripts in **ServerScriptService**:
- `Server/MainServer.lua` → **ServerScriptService** (as ServerScript)
- `Server/GamepassHandler.lua` → **ServerScriptService** (as ServerScript)

### 2. ReplicatedStorage
Create these folders and place modules in **ReplicatedStorage**:
- Create folder: `ModulesScript`
  - `ModulesScript/GameManager.lua` → **ReplicatedStorage/ModulesScript** (as ModuleScript)
  - `ModulesScript/PlayerData.lua` → **ReplicatedStorage/ModulesScript** (as ModuleScript)
  - `ModulesScript/CarSystem.lua` → **ReplicatedStorage/ModulesScript** (as ModuleScript)
  - `ModulesScript/CrimeSystem.lua` → **ReplicatedStorage/ModulesScript** (as ModuleScript)

- Create folder: `CarModels` (for car models)

### 3. StarterGui
Place this script in **StarterGui**:
- `StartGui/MainGui.lua` → **StarterGui** (as LocalScript)

### 4. StarterPlayer > StarterPlayerScripts
Place these scripts in **StarterPlayer > StarterPlayerScripts**:
- `Clients/MainClient.lua` → **StarterPlayerScripts** (as LocalScript)
- `Clients/CarShopClient.lua` → **StarterPlayerScripts** (as LocalScript)

**⚠️ IMPORTANT:** The StarterGui script must be placed BEFORE the StarterPlayerScripts to avoid GUI duplication!

## 🗺️ Workspace Setup

### Required Folders in Workspace:
1. **Cars** - Where spawned cars will be placed
2. **NPCs** - For NPCs around the map
3. **CrimeLocations** - Crime location markers
4. **CarSpawnPoints** - Car spawn locations

### NPC System Setup:
Create parts in `Workspace/NPCs` with these attributes:
- **NPC Spawn Points**: Set `SpawnPoint` attribute to `true`
- Place 4-8 spawn point parts around your map for optimal NPC distribution
- NPCs will automatically spawn at these locations

### Crime Locations Setup:
Create parts/models in `Workspace/CrimeLocations` with these attributes:
- **Bank**: Set `Type` attribute to "Bank"
- **Store**: Set `Type` attribute to "Store"  
- **ParkingLot**: Set `Type` attribute to "ParkingLot"
- **PoliceStation**: Set `Type` attribute to "PoliceStation"
- **Walls**: Set `Type` attribute to "Walls"

### Car Spawn Points Setup:
Create parts in `Workspace/CarSpawnPoints` with these attributes:
- **Civilian spawns**: Set `Category` attribute to "Civilian"
- **Police spawns**: Set `Category` attribute to "Police"
- **Criminal spawns**: Set `Category` attribute to "Criminal"
- **Special spawns**: Set `Category` attribute to "Special"

## 🚗 Car Models Setup

### In ReplicatedStorage/CarModels:
Create car models with these names:
- `BasicCarModel`
- `FamilyCarModel`
- `SportsCarModel`
- `PoliceCruiserModel`
- `SWATVehicleModel`
- `GetawayCarModel`
- `MotorcycleModel`
- `SupercarModel`
- `LuxuryCarModel`

### Car Model Requirements:
Each car model must have:
1. **PrimaryPart** set to the main body
2. **DriveSeat** (Seat or VehicleSeat) for the driver
3. **BodyVelocity** and **BodyAngularVelocity** will be added automatically

## 💎 Gamepass Setup

### In Server/GamepassHandler.lua:
Replace the gamepass IDs with your actual Roblox gamepass IDs:

```lua
local GAMEPASS_IDS = {
    VIPPass = 123456789, -- Replace with your VIP Pass ID
    SupercarPack = 123456790, -- Replace with your Supercar Pack ID
    SWATPack = 123456791, -- Replace with your SWAT Pack ID
    PrisonEscapePass = 123456792, -- Replace with your Prison Escape Pass ID
    GarageExpansion = 123456793, -- Replace with your Garage Expansion ID
    LuxuryCarsPack = 123456794 -- Replace with your Luxury Cars Pack ID
}
```

## 🎵 Sound Setup (Optional)

### Background Music:
1. Upload music to Roblox and get the Sound IDs
2. Create Sound objects in **SoundService** or **Workspace**
3. The sound toggle system will control these automatically

## 🎯 Game Controls

### Player Controls:
- **E** - Arrest nearby criminal players or NPCs (Police only)
- **M** - Toggle main menu
- **C** - Open/close car shop

### GUI Features:
- **Level/XP Bar** - Shows player progression
- **Cash Display** - Shows current money
- **Timer** - Shows round time remaining
- **Role Display** - Shows current role (Criminal/Police/Citizen)
- **Sound Buttons** - Toggle game sounds on/off

## ⚙️ Configuration

### Game Settings (in GameManager.lua):
```lua
GameManager.Settings = {
    MIN_PLAYERS = 3,        -- Minimum players to start
    MAX_PLAYERS = 12,       -- Maximum players in game
    ROUND_TIME = 300,       -- Round duration (5 minutes)
    INTERMISSION_TIME = 15, -- Time between rounds
    CRIMINAL_COUNT = 1,     -- Number of criminals per round
    POLICE_COUNT = 2        -- Number of police per round
}
```

### Car Prices (in CarSystem.lua):
Modify car prices and stats in the `CarConfigs` table.

### Crime Rewards (in CrimeSystem.lua):
Adjust crime rewards and difficulty in the `CrimeTypes` table.

## 🚀 Testing Your Game

### 1. Basic Testing:
1. Start Roblox Studio
2. Place all scripts in correct locations
3. Create required workspace folders
4. Test with multiple players (use "Test" > "Players" > set to 3+)

### 2. Test Checklist:
- [ ] Players can join and see GUI
- [ ] Roles are assigned when round starts
- [ ] Criminals receive crime missions
- [ ] Police can arrest criminal players
- [ ] NPCs spawn automatically around the map
- [ ] Police can arrest NPC criminals with E key
- [ ] NPC arrests give rewards (300 cash, 15 XP)
- [ ] Car shop opens with C key
- [ ] Cars can be purchased and spawned
- [ ] Level/XP system works
- [ ] Sound buttons function
- [ ] Menu opens with M key

### 3. Common Issues:
- **Scripts not running**: Check script types (LocalScript vs ServerScript)
- **RemoteEvents not found**: Ensure MainServer.lua runs first
- **Cars not spawning**: Check car models exist in ReplicatedStorage/CarModels
- **GUI not showing**: Verify LocalScripts are in correct locations

## 📊 Data Storage

### DataStore Setup:
The game uses Roblox DataStoreService to save:
- Player cash and level
- Owned cars and items
- Game statistics
- Settings preferences

**Note**: DataStores only work in published games, not in Studio testing.

## 🎨 Customization Ideas

### Easy Modifications:
1. **Add new cars**: Create models and add to CarConfigs
2. **New crimes**: Add to CrimeTypes in CrimeSystem.lua
3. **Change colors**: Modify Color3.fromRGB values in GUI scripts
4. **Adjust rewards**: Change cash/XP amounts in configurations

### Advanced Features:
1. **Weapons system**: Add tools for police/criminals
2. **Prison system**: Create jail area with escape mechanics
3. **Team chat**: Add role-specific communication
4. **Leaderboards**: Display top players
5. **Daily rewards**: Login bonuses and challenges

## 🐛 Troubleshooting

### Script Errors:
1. Check Output window for error messages
2. Ensure all required folders exist in Workspace
3. Verify script placement matches this guide
4. Check that ModuleScripts are in ReplicatedStorage

### Performance Issues:
1. Limit number of spawned cars
2. Clean up old crime data regularly
3. Optimize GUI animations
4. Use efficient car physics

## 📞 Support

If you encounter issues:
1. Check the Output window for errors
2. Verify all scripts are placed correctly
3. Ensure workspace folders are created
4. Test with the minimum required players (3)

## 🎉 Launch Checklist

Before publishing your game:
- [ ] All scripts placed correctly
- [ ] Workspace folders created
- [ ] Car models added
- [ ] Gamepass IDs updated
- [ ] Game tested with multiple players
- [ ] DataStore permissions enabled
- [ ] Game description and thumbnail ready

**Congratulations! Your crime game is ready to play! 🎮**
