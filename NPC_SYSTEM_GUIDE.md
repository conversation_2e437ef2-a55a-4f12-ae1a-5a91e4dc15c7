# 🤖 NPC System Guide - Complete Implementation

## 📋 Overview

The NPC System adds intelligent Non-Player Characters to your crime game, providing:
- **NPC Criminals** that can be arrested by police players
- **NPC Civilians** for atmosphere and realism
- **Smart AI behavior** including crime simulation and police avoidance
- **Automatic spawning and management** of NPCs around the map

## 🎯 Features Implemented

### ✅ Core NPC System
- **NPCSystem.lua** - Complete NPC management module
- **Automatic NPC spawning** at designated spawn points
- **NPC behavior AI** with different patterns for criminals and civilians
- **NPC arrest mechanics** integrated with existing arrest system

### ✅ NPC Types

#### 🚨 Criminal NPCs
- **Health**: 100 HP
- **Speed**: 12 walk, 18 run
- **Arrest Reward**: 300 cash + 15 XP
- **Behavior**: Wander, investigate crime locations, avoid police
- **Visual**: Red colored with "🚨 CRIMINAL" tag

#### 👤 Civilian NPCs  
- **Health**: 100 HP
- **Speed**: 10 walk, 14 run
- **Arrest Reward**: None (cannot be arrested)
- **Behavior**: Peaceful wandering
- **Visual**: Blue colored, no special tags

### ✅ AI Behaviors

#### Criminal AI:
1. **Crime Location Investigation** - Move toward banks, stores, etc.
2. **Police Avoidance** - Run away when police players are nearby
3. **Suspicious Movement** - Erratic patterns and sudden sprinting
4. **Normal Wandering** - Random movement around spawn area

#### Civilian AI:
1. **Peaceful Wandering** - Slow, calm movement in small areas

## 🛠️ Setup Instructions

### 1. Script Placement
The NPCSystem is already integrated into your existing structure:
- `ModulesScript/NPCSystem.lua` ✅ Created
- `Server/MainServer.lua` ✅ Updated to include NPCSystem
- `Clients/MainClient.lua` ✅ Updated for NPC arrest detection

### 2. Workspace Setup

#### Create NPCs Folder:
1. In **Workspace**, create a folder named `NPCs`
2. This folder will contain NPC spawn points and spawned NPCs

#### Create NPC Spawn Points:
1. In the `NPCs` folder, create **Part** objects
2. Position them around your map where you want NPCs to spawn
3. For each spawn point part, add this attribute:
   - **Name**: `SpawnPoint`
   - **Type**: `Boolean`
   - **Value**: `true`

#### Recommended Spawn Point Locations:
- Near crime locations (banks, stores)
- Around streets and sidewalks
- Near police stations
- In parking lots
- Around civilian areas

### 3. Spawn Point Configuration

```lua
-- Example spawn point setup in Studio:
local spawnPoint = Instance.new("Part")
spawnPoint.Name = "NPCSpawn1"
spawnPoint.Size = Vector3.new(4, 1, 4)
spawnPoint.Material = Enum.Material.Neon
spawnPoint.BrickColor = BrickColor.new("Bright green")
spawnPoint.Anchored = true
spawnPoint.CanCollide = false
spawnPoint.Transparency = 0.5
spawnPoint:SetAttribute("SpawnPoint", true)
spawnPoint.Parent = workspace.NPCs
```

## 🎮 How to Use

### For Police Players:
1. **Look for NPCs**: Criminal NPCs have red color and "🚨 CRIMINAL" tags
2. **Get Close**: Move within 15 studs of an NPC criminal
3. **Press E**: Use the arrest key to arrest the NPC
4. **Earn Rewards**: Get 300 cash and 15 XP per NPC arrest

### NPC Behavior:
- **Criminal NPCs** will move around the map, sometimes running suspiciously
- **They avoid police players** when they get too close
- **They investigate crime locations** like banks and stores
- **Maximum 8 NPCs** spawn at once (configurable)
- **New NPCs spawn every 5 seconds** if below maximum

## ⚙️ Configuration

### NPC Settings (in NPCSystem.lua):
```lua
NPCSystem.MaxNPCs = 8              -- Maximum NPCs at once
NPCSystem.SpawnCooldown = 5        -- Seconds between spawns

-- Criminal NPC Settings:
Criminal = {
    Health = 100,
    WalkSpeed = 12,
    RunSpeed = 18,
    ArrestReward = 300,            -- Cash reward
    XPReward = 15,                 -- XP reward
    SpawnChance = 0.7              -- 70% chance to spawn criminal
}

-- Civilian NPC Settings:
Civilian = {
    Health = 100,
    WalkSpeed = 10,
    RunSpeed = 14,
    SpawnChance = 0.3              -- 30% chance to spawn civilian
}
```

## 🔧 Advanced Features

### NPC Management Functions:
```lua
-- Spawn specific NPC type
NPCSystem:SpawnNPC("Criminal", spawnPoint)

-- Get nearest NPC to position
local npc, distance = NPCSystem:GetNearestNPC(position, maxDistance)

-- Get all NPCs of specific type
local criminals = NPCSystem:GetNPCsByType("Criminal")

-- Destroy all NPCs (useful for round resets)
NPCSystem:DestroyAllNPCs()
```

### Events Available:
```lua
-- When NPC is spawned
NPCSystem.Events.NPCSpawned.Event:Connect(function(npc, npcType)
    print("NPC spawned:", npcType)
end)

-- When NPC is arrested
NPCSystem.Events.NPCArrested.Event:Connect(function(npc, policePlayer, cashReward, xpReward)
    print(policePlayer.Name, "arrested NPC for", cashReward, "cash")
end)
```

## 🐛 Troubleshooting

### NPCs Not Spawning:
1. ✅ Check that `NPCs` folder exists in Workspace
2. ✅ Verify spawn points have `SpawnPoint = true` attribute
3. ✅ Ensure NPCSystem is initialized in MainServer.lua
4. ✅ Check Output window for error messages

### NPCs Not Moving:
1. ✅ Verify NPCs have Humanoid and PrimaryPart set
2. ✅ Check that spawn points are positioned correctly
3. ✅ Ensure no parts are blocking NPC movement

### Arrest Not Working:
1. ✅ Confirm player has "Police" role
2. ✅ Check distance (must be within 15 studs)
3. ✅ Verify target is a Criminal NPC (red with tag)
4. ✅ Ensure arrestRequest RemoteFunction exists

### Performance Issues:
1. ✅ Reduce `MaxNPCs` if too many NPCs cause lag
2. ✅ Increase `SpawnCooldown` to spawn NPCs less frequently
3. ✅ Check that old NPCs are being cleaned up properly

## 📊 Statistics Integration

The NPC system integrates with your existing player statistics:
- **CriminalsArrested** stat increases when arresting NPCs
- **Cash and XP** are awarded through PlayerData system
- **Arrest rewards** are configurable per NPC type

## 🎨 Customization Ideas

### Easy Modifications:
1. **Change NPC appearance**: Modify colors and add accessories in `CreateNPCModel()`
2. **Adjust rewards**: Change `ArrestReward` and `XPReward` values
3. **Modify spawn rates**: Adjust `SpawnChance` for different NPC types
4. **Change behavior**: Modify AI patterns in behavior functions

### Advanced Features to Add:
1. **NPC Weapons**: Give criminal NPCs tools or weapons
2. **NPC Dialogue**: Add chat messages or speech bubbles
3. **NPC Missions**: Create specific tasks for NPCs to complete
4. **NPC Teams**: Add gang affiliations or police NPC allies
5. **NPC Vehicles**: Allow NPCs to drive cars around the map

## 🎉 Success Indicators

Your NPC system is working correctly when you see:
- ✅ NPCs spawning automatically around the map
- ✅ Criminal NPCs with red color and criminal tags
- ✅ NPCs moving and exhibiting different behaviors
- ✅ Police players can arrest NPCs with E key
- ✅ Arrest rewards being given (300 cash, 15 XP)
- ✅ Console messages showing NPC activities

## 📞 Support

If you encounter issues:
1. Check the Output window for error messages
2. Verify all spawn points are set up correctly
3. Ensure the NPCs folder exists in Workspace
4. Test with a police role player in-game

**Congratulations! Your crime game now has intelligent NPCs! 🎮🤖**
